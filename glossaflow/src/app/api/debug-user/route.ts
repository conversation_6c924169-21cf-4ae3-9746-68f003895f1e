import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log('Debug user lookup:', {
      userId,
      userEmail,
    });

    // Check if user exists by ID
    const { data: userById, error: userByIdError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    // Check if user exists by email
    const { data: userByEmail, error: userByEmailError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single();

    // Get all users to see what's in the table
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, email, name')
      .limit(10);

    return NextResponse.json({
      session: {
        userId,
        userEmail,
        userName: session.user.name,
      },
      userById: {
        data: userById,
        error: userByIdError,
      },
      userByEmail: {
        data: userByEmail,
        error: userByEmailError,
      },
      allUsers: {
        data: allUsers,
        error: allUsersError,
      },
      success: true,
    });
  } catch (error) {
    console.error('Debug user error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
